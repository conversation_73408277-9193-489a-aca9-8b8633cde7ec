import re
import json
from bs4 import BeautifulSoup

def extract_bbb_profile(html_content):
    """Extract business information from BBB profile HTML and return as JSON."""
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Initialize data dictionary
    business_data = {
        "business_name": "",
        "business_type": "",
        "accreditation": "",
        "phone": "",
        "address": "",
        "rating": "",
        "business_details": {},
        "management": {},
        "business_categories": []
    }
    
    # Extract business name
    business_name = soup.find('title')
    if business_name:
        business_data["business_name"] = business_name.text.split('|')[0].strip()
    
    # Extract business type
    business_type = soup.select_one('a[href*="/category/dentist"]')
    if business_type:
        business_data["business_type"] = business_type.text.strip()
    
    # Extract accreditation status
    accreditation = soup.find(string=re.compile("This business is (NOT )?BBB Accredited"))
    if accreditation:
        business_data["accreditation"] = "Not Accredited" if "NOT" in accreditation else "Accredited"
    
    # Extract phone number
    phone = soup.find(string=re.compile(r'\(\d{3}\) \d{3}-\d{4}'))
    if phone:
        business_data["phone"] = phone.strip()
    
    # Extract address
    address_lines = []
    address_section = soup.find('div', class_=lambda c: c and 'address' in c.lower())
    if address_section:
        for line in address_section.stripped_strings:
            if "View Service Area" not in line:
                address_lines.append(line)
        business_data["address"] = " ".join(address_lines)
    
    # Extract rating
    rating = soup.find(string=re.compile(r'BBB Rating'))
    if rating:
        rating_value = rating.find_next(string=re.compile(r'[A-F][+\-]?'))
        if rating_value:
            business_data["rating"] = rating_value.strip()
    
    # Extract business details
    details_section = soup.find('h2', string=re.compile('Business Details'))
    if details_section:
        details_div = details_section.find_parent('div')
        if details_div:
            for dt in details_div.find_all('dt'):
                key = dt.text.strip().rstrip(':')
                dd = dt.find_next('dd')
                if dd:
                    business_data["business_details"][key] = dd.text.strip()
    
    # Extract management info
    management_section = soup.find(string=re.compile('Business Management'))
    if management_section:
        management_value = management_section.find_next('dd')
        if management_value:
            name_parts = management_value.text.strip().split(',')
            if len(name_parts) >= 2:
                business_data["management"]["name"] = name_parts[0].strip()
                business_data["management"]["title"] = ','.join(name_parts[1:]).strip()
            else:
                business_data["management"]["name"] = management_value.text.strip()
    
    # Extract business categories
    categories_section = soup.find('h2', string=re.compile('Business Categories'))
    if categories_section:
        categories = categories_section.find_next('div').find_all('a')
        for category in categories:
            business_data["business_categories"].append(category.text.strip())
    
    return business_data

def main():
    # Replace with your file path
    file_path = r"html-files\3.html"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            html_content = file.read()
        
        business_data = extract_bbb_profile(html_content)
        
        # Output as JSON
        print(json.dumps(business_data, indent=2))
        
        # Save to file
        with open('profile_data.json', 'w', encoding='utf-8') as f:
            json.dump(business_data, f, indent=2)
        
        print("Data successfully extracted and saved to bbb_profile_data.json")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
